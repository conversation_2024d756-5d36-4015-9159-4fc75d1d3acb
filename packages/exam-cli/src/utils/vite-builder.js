import { readFileSync, existsSync } from 'node:fs';
import { join, resolve } from 'node:path';
import { build as viteBuild, createServer as viteCreateServer } from 'vite';
import { createViteConfig, createAliasConfig } from '@bzl/zhice-exam-env/vite';
import { CLIError } from './error-handler.js';
import chalk from 'chalk';

/**
 * Vite 构建工具类
 * 支持无配置文件的构建方式
 */
export class ViteBuilder {
    constructor(options = {}) {
        this.options = options;
        this.cwd = options.cwd || process.cwd();
    }

    /**
     * 自动检测应用类型
     */
    detectAppType(appPath = this.cwd) {
        // 1. 检查 package.json 中的 exam.appType 字段
        const packageJsonPath = join(appPath, 'package.json');
        if (existsSync(packageJsonPath)) {
            try {
                const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));

                // 优先使用配置中的 appType
                if (packageJson.exam?.appType) {
                    return packageJson.exam.appType;
                }

                // 根据包名推断
                if (packageJson.name) {
                    if (packageJson.name.includes('admin')) return 'admin';
                    if (packageJson.name.includes('user')) return 'user';
                }
            } catch (error) {
                console.warn(chalk.yellow(`警告: 无法读取 package.json: ${error.message}`));
            }
        }

        // 2. 根据目录路径推断
        const normalizedPath = appPath.replace(/\\/g, '/');
        if (normalizedPath.includes('/apps/admin') || normalizedPath.endsWith('/admin')) {
            return 'admin';
        }
        if (normalizedPath.includes('/apps/user') || normalizedPath.endsWith('/user')) {
            return 'user';
        }

        // 3. 检查特征文件
        if (existsSync(join(appPath, 'src/router-v2'))) {
            return 'admin'; // admin 应用特有的路由结构
        }
        if (existsSync(join(appPath, 'login.html'))) {
            return 'user'; // user 应用有 login.html
        }

        throw new CLIError('无法自动检测应用类型，请在 package.json 中添加 exam.appType 字段');
    }

    /**
     * 加载自定义配置
     */
    async loadCustomConfig(appPath = this.cwd) {
        const configFiles = ['exam.config.js', 'exam.config.mjs', '.examrc.js', '.examrc.mjs'];

        for (const configFile of configFiles) {
            const configPath = join(appPath, configFile);
            if (existsSync(configPath)) {
                try {
                    // 动态导入配置文件
                    const config = await import(configPath);
                    return config.default || config;
                } catch (error) {
                    console.warn(chalk.yellow(`警告: 无法加载配置文件 ${configFile}: ${error.message}`));
                }
            }
        }

        // 检查 package.json 中的 exam 字段
        const packageJsonPath = join(appPath, 'package.json');
        if (existsSync(packageJsonPath)) {
            try {
                const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
                if (packageJson.exam?.vite) {
                    return packageJson.exam.vite;
                }
            } catch (error) {
                console.warn(chalk.yellow(`警告: 无法读取 package.json: ${error.message}`));
            }
        }

        return {};
    }

    /**
     * 创建 Vite 配置
     */
    async createConfig(appPath, mode = 'production') {
        const appType = this.detectAppType(appPath);
        const customConfig = await this.loadCustomConfig(appPath);

        console.log(chalk.blue(`🔍 检测到应用类型: ${chalk.cyan(appType)}`));

        if (Object.keys(customConfig).length > 0) {
            console.log(chalk.blue(`📝 加载自定义配置: ${Object.keys(customConfig).join(', ')}`));
        }

        // 创建别名配置
        const aliasConfig = createAliasConfig(appPath);

        // 使用 exam-env 创建配置
        const viteConfig = createViteConfig({
            appType,
            configEnv: { command: mode === 'development' ? 'serve' : 'build', mode },
            cwd: appPath,
            aliasConfig,
            customConfig,
        });

        return viteConfig;
    }

    /**
     * 构建应用
     */
    async build(appPath = this.cwd, mode = 'production') {
        try {
            console.log(chalk.cyan(`🏗️  开始构建应用...`));
            console.log(chalk.gray(`   路径: ${appPath}`));
            console.log(chalk.gray(`   模式: ${mode}`));

            const viteConfig = await this.createConfig(appPath, mode);

            // 设置工作目录
            viteConfig.root = appPath;

            if (this.options.verbose) {
                console.log(chalk.gray('📋 Vite 配置:'));
                console.log(JSON.stringify(viteConfig, null, 2));
            }

            // 执行构建
            await viteBuild(viteConfig);

            console.log(chalk.green.bold('✅ 构建完成!'));
            return { success: true };
        } catch (error) {
            console.error(chalk.red.bold('❌ 构建失败:'), error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 启动开发服务器
     */
    async dev(appPath = this.cwd, mode = 'development') {
        try {
            console.log(chalk.cyan(`🚀 启动开发服务器...`));
            console.log(chalk.gray(`   路径: ${appPath}`));
            console.log(chalk.gray(`   模式: ${mode}`));

            const viteConfig = await this.createConfig(appPath, mode);

            // 设置工作目录
            viteConfig.root = appPath;

            // 覆盖命令类型为 serve
            viteConfig.command = 'serve';

            if (this.options.verbose) {
                console.log(chalk.gray('📋 Vite 配置:'));
                console.log(JSON.stringify(viteConfig, null, 2));
            }

            // 创建开发服务器
            const server = await viteCreateServer(viteConfig);

            // 启动服务器
            await server.listen();

            server.printUrls();

            return server;
        } catch (error) {
            console.error(chalk.red.bold('❌ 启动开发服务器失败:'), error.message);
            throw error;
        }
    }

    /**
     * 检查是否已有 vite.config.ts
     */
    hasViteConfig(appPath = this.cwd) {
        const configFiles = ['vite.config.js', 'vite.config.ts', 'vite.config.mjs', 'vite.config.mts'];

        return configFiles.some((file) => existsSync(join(appPath, file)));
    }
}
